import type { db } from '$lib/database';
import type { AuthUser, Session } from '$lib/types/auth';

declare global {
	namespace App {
		interface Locals {
			db: typeof db;
			user: AuthUser | null;
			session: Session | null;
			can: {
				check: (entity: string, action: string) => Promise<boolean>;
				read: (entity: string) => Promise<boolean>;
				create: (entity: string) => Promise<boolean>;
				update: (entity: string) => Promise<boolean>;
				delete: (entity: string) => Promise<boolean>;
			};
		}
		interface PageData {
			user: AuthUser | null;
			session: Session | null;
		}
	}
}

export {};
