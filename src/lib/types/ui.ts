/**
 * UI-specific types for NextYa application
 * These types are for UI components, forms, and user interactions
 */

import type { ToastType } from './core';
import type { ApiOmrErrorData, ApiOmrSuccessData } from './api';

// Toast and notification types
export interface ToastState {
	id: number;
	title: string;
	type: ToastType;
}

// Table component types
export interface TableColumn<T = unknown> {
	key?: string;
	label: string;
	headerClass?: string;
	class?: string;
	cell?: (row: T) => unknown;
}

// File upload and processing types
export type UIFileStatus = 'pending' | 'processing' | 'success' | 'error';

export interface FileEntry {
	file: File;
	id: string;
	status: UIFileStatus;
	result: ApiOmrSuccessData | null;
	error: ApiOmrErrorData | null;
	saved: boolean;
	formatValid: boolean;
	formatName: string;
}

// Form types
export interface UserMetadata {
	name?: string;
	last_name?: string;
	role?: string;
	photo_url?: string;
}

export interface LoginHistoryItem {
	date: string;
	ip: string;
	device: string;
	status: 'success' | 'failed';
	location?: string;
}

export interface LevelWithUsers {
	code: string;
	name: string;
	users?: string[];
}

// Component prop types
export interface FileUploadProps {
	dis?: boolean;
	multiple?: boolean;
	accept?: string;
	variant?: 'primary' | 'outline';
	size?: 'sm' | 'md' | 'lg';
	icon?: 'plus' | 'upload';
	text?: string;
	class?: string;
	onFileSelect: (files: FileList) => void;
}

export interface StatCardProps {
	title: string;
	value: string | number;
	percentage: number;
	colorClass: string;
	borderClass: string;
	borderColor?: string;
	subtitle: string;
}

// Message component types
export interface MessageProps {
	type?: 'success' | 'warning' | 'danger' | 'info';
	description?: string;
}

// Permission types for UI (UI-specific variants)
export interface UIEntity {
	name: string;
	label: string;
}

export type UIAction = 'read' | 'create' | 'update' | 'delete';

export interface UIEntityPermissions {
	read: boolean;
	create: boolean;
	update: boolean;
	delete: boolean;
}

export interface UIPermissionCheck {
	entity: string;
	action: UIAction;
}

// Dashboard chart types
export interface EvalChartData {
	name: string;
	averageScore: number;
}

export interface GroupChartData {
	group: string;
	averageScore: number;
}

export interface AnswerDistribution {
	correct: number;
	incorrect: number;
	blank: number;
}

export interface StudentPerformance {
	name: string;
	averageScore: number;
}

export interface LevelDashboardData {
	scoresByGroup: GroupChartData[];
	correctVsIncorrect: AnswerDistribution;
}

// Event handler types
export type EventListener = (event: Event) => void;

// Navigation and routing types
export interface PageData {
	user: any | null;
	session: any | null;
	title?: string;
}

// Form validation types
export interface ValidationError {
	field: string;
	message: string;
	code?: string;
}

export interface FormState {
	isSubmitting: boolean;
	errors: ValidationError[];
	success: boolean;
	message?: string;
}

// Modal and dialog types
export interface ModalState {
	isOpen: boolean;
	title?: string;
	content?: string;
	confirmText?: string;
	cancelText?: string;
	onConfirm?: () => void;
	onCancel?: () => void;
}

// Pagination types
export interface PaginationState {
	currentPage: number;
	totalPages: number;
	pageSize: number;
	totalItems: number;
}

// Search and filter types
export interface SearchState {
	query: string;
	filters: Record<string, any>;
	sortBy?: string;
	sortOrder?: 'asc' | 'desc';
}

// Loading states
export interface LoadingState {
	isLoading: boolean;
	message?: string;
	progress?: number;
}

// Theme types
export type ThemeMode = 'light' | 'dark' | 'system';

export interface ThemeState {
	mode: ThemeMode;
	isDark: boolean;
}
