/**
 * Core domain types for NextYa application
 * These types represent the core business entities and should be stable
 */

// Re-export database types for convenience
export type {
	Users,
	Students,
	Courses,
	Levels,
	Evals,
	EvalSections,
	EvalQuestions,
	EvalAnswers,
	EvalResults,
	Registers,
	Permissions,
	DB
} from '$lib/database/types';

// Core domain entities with business logic
export interface User {
	code: string;
	email: string;
	name: string | null;
	lastName: string | null;
	photoUrl: string | null;
	lastLogin: Date | null;
	isEmailVerified: boolean;
	isSuperAdmin: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface Student {
	code: string;
	name: string;
	lastName: string;
	email: string;
	phone: string | null;
	userCode: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface Course {
	code: string;
	name: string;
	abr: string;
	userCode: string;
	order: number;
	createdAt: Date;
}

export interface Level {
	code: string;
	name: string;
	abr: string;
	users: string[];
	createdAt: Date;
}

export interface Eval {
	code: string;
	name: string;
	evalDate: Date;
	groupName: string;
	levelCode: string;
	userCode: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface EvalSection {
	code: string;
	evalCode: string;
	courseCode: string;
	orderInEval: number;
	questionCount: number;
}

export interface EvalQuestion {
	code: string;
	evalCode: string;
	sectionCode: string;
	correctKey: string;
	orderInEval: number;
	scorePercent: number;
	omitable: boolean;
}

export interface EvalAnswer {
	code: string;
	questionCode: string;
	registerCode: string;
	studentAnswer: string | null;
}

export interface EvalResult {
	code: string;
	evalCode: string;
	registerCode: string;
	sectionCode: string | null;
	correctCount: number;
	incorrectCount: number;
	blankCount: number;
	score: number;
	calculatedAt: Date;
}

export interface Register {
	code: string;
	studentCode: string;
	levelCode: string;
	groupName: string;
	rollCode: string;
	userCode: string;
	createdAt: Date;
}

export interface Permission {
	code: string;
	userCode: string;
	entity: string;
	action: string;
	createdAt: Date;
}

// Common value objects
export type EntityType =
	| 'levels'
	| 'courses'
	| 'students'
	| 'registers'
	| 'evals'
	| 'eval_sections'
	| 'eval_questions'
	| 'eval_answers'
	| 'eval_results';

export type AnswerValue = 'A' | 'B' | 'C' | 'D' | 'E' | null | 'error_multiple';

export type ToastType = 'success' | 'danger' | 'warning' | 'info';

export type FileStatus = 'pending' | 'processing' | 'success' | 'error';

// Composite types for business operations
export interface EvalSectionWithCourse extends EvalSection {
	course_name?: string;
	courses?: { name: string };
}

export interface EvalWithSections extends Eval {
	eval_sections: EvalSectionWithCourse[];
	levels?: { name: string };
}

export interface RegisterStudent {
	student_code: string;
	register_code: string;
	name: string;
	last_name: string;
	level_code: string;
	email: string;
	phone: string | null;
	roll_code: string;
	group_name: string;
	level: string;
	created_at: string;
}

export interface SimpleUser {
	id: string;
	name: string;
	last_name: string;
}

export interface ResultItem {
	result_code: string;
	register_code: string;
	eval_code: string;
	section_code: string | null;
	correct_count: number;
	incorrect_count: number;
	blank_count: number;
	score: number;
	calculated_at: string;
	student_code: string;
	roll_code: string;
	group_name: string;
	level_code: string;
	name: string;
	last_name: string;
	level_name: string;
}

export interface SelectForDelete {
	code: string;
	register_code: string;
	name: string;
	mode: 'all' | 'only_register';
}

export interface FormSection {
	course_code: string;
	course_name: string;
	order_in_eval: number;
	question_count: number;
}
