/**
 * Centralized type exports for NextYa application
 * Following clean architecture principles with clear separation of concerns
 */

// Core domain types
export * from './core';

// Authentication and authorization types
export * from './auth';

// UI and component types
export * from './ui';

// API and external interface types
export * from './api';
export * from './app';

// Feature-specific types
export * from './studentResults';
export * from './studentExport';
export * from './courseDashboard';
export * from './studentDashboard';
export * from './evalDashboard';
export * from './permissions';
export * from './table';

// Legacy compatibility exports (to be gradually removed)
export type { ApiError } from './apiError';
