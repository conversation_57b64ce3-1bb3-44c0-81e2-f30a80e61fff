/**
 * Centralized type exports for NextYa application
 * Following clean architecture principles with clear separation of concerns
 */

// Core domain types (primary exports)
export * from './core';

// Authentication and authorization types (with prefixed names to avoid conflicts)
export type {
	JWTPayload,
	AuthUser,
	Session,
	LoginCredentials,
	SignupData,
	AuthResponse,
	LoginResponse,
	SignupResponse,
	AuthPermission,
	PermissionAction,
	AuthPermissionCheck,
	AuthEntityPermissions,
	ApiPermission,
	UserCreateData,
	UserUpdateData,
	UserPasswordUpdate,
	SessionOptions,
	AuthContext,
	Role,
	UserRole
} from './auth';

// UI and component types (with UI prefix to avoid conflicts)
export type {
	ToastState,
	TableColumn,
	FileEntry,
	UIFileStatus,
	UserMetadata,
	LoginHistoryItem,
	LevelWithUsers,
	FileUploadProps,
	StatCardProps,
	MessageProps,
	UIEntity,
	UIAction,
	UIEntityPermissions,
	UIPermissionCheck,
	EvalChartData,
	GroupChartData,
	AnswerDistribution,
	StudentPerformance,
	LevelDashboardData,
	EventListener,
	PageData,
	ValidationError,
	FormState,
	ModalState,
	PaginationState,
	SearchState,
	LoadingState,
	ThemeMode,
	ThemeState
} from './ui';

// API and external interface types
export * from './api';

// Application-level types (convenience re-exports)
export type { FileStatus } from './app';

// Feature-specific types
export * from './studentResults';
export * from './studentExport';
export * from './courseDashboard';
export * from './studentDashboard';
export * from './evalDashboard';
export * from './permissions';
export * from './table';

// Legacy compatibility exports (to be gradually removed)
export type { ApiError } from './apiError';
