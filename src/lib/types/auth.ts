/**
 * Authentication and authorization types for NextYa application
 */

// JWT and session types
export interface JWTPayload {
	userCode: string;
	email: string;
	iat?: number;
	exp?: number;
}

export interface AuthUser {
	code: string;
	email: string;
	name: string | null;
	lastName: string | null;
	lastLogin: Date | null;
}

export interface Session {
	user: AuthUser;
	token: string;
	expiresAt: number;
}

// Authentication form types
export interface LoginCredentials {
	email: string;
	password: string;
}

export interface SignupData {
	email: string;
	password: string;
	name?: string;
	lastName?: string;
}

// Authentication response types
export interface AuthResponse {
	success: boolean;
	user?: AuthUser;
	error?: string;
}

export interface LoginResponse extends AuthResponse {
	session?: Session;
}

export interface SignupResponse extends AuthResponse {
	session?: Session;
}

// Permission types (auth-specific)
export interface AuthPermission {
	code: string;
	userCode: string;
	entity: string;
	action: string;
	createdAt: Date;
}

export type PermissionAction = 'read' | 'create' | 'update' | 'delete';

export interface AuthPermissionCheck {
	entity: string;
	action: PermissionAction;
}

export interface AuthEntityPermissions {
	read: boolean;
	create: boolean;
	update: boolean;
	delete: boolean;
}

// API permission types (for compatibility with existing code)
export interface ApiPermission {
	code: string;
	user_code: string;
	entity: string;
	user_action: string;
}

// User management types
export interface UserCreateData {
	email: string;
	name: string;
	lastName: string;
	password: string;
	photoUrl?: string;
}

export interface UserUpdateData {
	email?: string;
	name?: string;
	lastName?: string;
	photoUrl?: string;
}

export interface UserPasswordUpdate {
	userId: string;
	password: string;
}

// Session management
export interface SessionOptions {
	httpOnly: boolean;
	secure: boolean;
	sameSite: 'strict' | 'lax' | 'none';
	maxAge: number;
	path: string;
}

// Authorization context
export interface AuthContext {
	user: AuthUser | null;
	session: Session | null;
	permissions: AuthPermission[];
	can: {
		check: (entity: string, action: string) => Promise<boolean>;
		read: (entity: string) => Promise<boolean>;
		create: (entity: string) => Promise<boolean>;
		update: (entity: string) => Promise<boolean>;
		delete: (entity: string) => Promise<boolean>;
	};
}

// Role-based access control (future extension)
export interface Role {
	code: string;
	name: string;
	description?: string;
	permissions: AuthPermission[];
}

export interface UserRole {
	userCode: string;
	roleCode: string;
	assignedAt: Date;
	assignedBy: string;
}
