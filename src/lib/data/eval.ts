import { db } from '$lib/database';
import type { EvalSectionWithCourse } from '$lib/types';

export async function fetchSections(evalCode: string): Promise<EvalSectionWithCourse[]> {
	try {
		const sections = await db
			.selectFrom('evalSections')
			.innerJoin('courses', 'courses.code', 'evalSections.courseCode')
			.select([
				'evalSections.code',
				'evalSections.evalCode',
				'evalSections.courseCode',
				'evalSections.orderInEval',
				'evalSections.questionCount',
				'courses.name as courseName'
			])
			.where('evalSections.evalCode', '=', evalCode)
			.orderBy('evalSections.orderInEval', 'asc')
			.execute();

		// Transform to match expected EvalSectionWithCourse interface
		return sections.map((section) => ({
			code: section.code,
			evalCode: section.eval_code,
			courseCode: section.course_code,
			orderInEval: section.order_in_eval,
			questionCount: section.question_count,
			course_name: section.course?.name,
			courses: { name: section.course?.name || '' }
		})) as EvalSectionWithCourse[];
	} catch (error) {
		console.error('Error fetching sections:', error);
		return [];
	}
}
