/*
  Utility styles for consistent UI components
  This file contains reusable styles for common UI patterns
*/

/* Card variations */
.card-gradient-primary {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-primary) 20%, transparent),
		color-mix(in srgb, var(--color-primary) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-primary) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-primary:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-secondary {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-secondary) 20%, transparent),
		color-mix(in srgb, var(--color-secondary) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-secondary) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-secondary:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-accent {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-accent) 20%, transparent),
		color-mix(in srgb, var(--color-accent) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-accent) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-accent:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-info {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-info) 20%, transparent),
		color-mix(in srgb, var(--color-info) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-info) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-info:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-success {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-success) 20%, transparent),
		color-mix(in srgb, var(--color-success) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-success) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-success:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-warning {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-warning) 20%, transparent),
		color-mix(in srgb, var(--color-warning) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-warning) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-warning:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-error {
	background: linear-gradient(
		to bottom right,
		color-mix(in srgb, var(--color-error) 20%, transparent),
		color-mix(in srgb, var(--color-error) 5%, transparent)
	);
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-error) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-error:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

.card-gradient-neutral {
	background: linear-gradient(to bottom right, var(--color-base-200), var(--color-base-100));
	box-shadow:
		0 1px 2px 0 rgba(0, 0, 0, 0.02),
		0 1px 4px -1px rgba(0, 0, 0, 0.01);
	border: 1px solid color-mix(in srgb, var(--color-base-300) 30%, transparent);
	transition:
		transform 0.2s ease,
		box-shadow 0.2s ease;
}

.card-gradient-neutral:hover {
	box-shadow:
		0 4px 6px -1px rgba(0, 0, 0, 0.03),
		0 2px 4px -1px rgba(0, 0, 0, 0.02);
	transform: translateY(-2px);
}

/* Icon containers */
.icon-container {
	padding: 0.75rem;
	border-radius: 9999px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.icon-container-primary {
	background-color: color-mix(in srgb, var(--color-primary) 20%, transparent);
	color: var(--color-primary);
}

.icon-container-secondary {
	background-color: color-mix(in srgb, var(--color-secondary) 20%, transparent);
	color: var(--color-secondary);
}

.icon-container-accent {
	background-color: color-mix(in srgb, var(--color-accent) 20%, transparent);
	color: var(--color-accent);
}

.icon-container-info {
	background-color: color-mix(in srgb, var(--color-info) 20%, transparent);
	color: var(--color-info);
}

.icon-container-success {
	background-color: color-mix(in srgb, var(--color-success) 20%, transparent);
	color: var(--color-success);
}

.icon-container-warning {
	background-color: color-mix(in srgb, var(--color-warning) 20%, transparent);
	color: var(--color-warning);
}

.icon-container-error {
	background-color: color-mix(in srgb, var(--color-error) 20%, transparent);
	color: var(--color-error);
}

/* Form groups */
.form-group {
	margin-bottom: 0.5rem;
}

.form-group-label {
	display: flex;
	font-weight: 500;
	padding: 0.5rem 0;
}

.form-group-hint {
	font-size: 0.75rem;
	opacity: 0.7;
	margin-top: 0.25rem;
	display: block;
}

/* Fieldsets */
.fieldset-container {
	display: grid;
	grid-template-columns: repeat(1, minmax(0, 1fr));
	gap: 1.5rem;
	padding: 1rem;
	border-radius: 1rem;
	border: 1px solid var(--color-base-300);
	background-color: var(--color-base-200);
}

@media (min-width: 768px) {
	.fieldset-container {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}
}

/* Page sections */
.page-section {
	margin-bottom: 2rem;
}

.page-section-title {
	font-size: 1.5rem;
	font-weight: 600;
	margin-bottom: 1rem;
}

/* Data display */
.data-display {
	padding: 1rem;
	background-color: var(--color-base-200);
	border-radius: 1rem;
	margin-bottom: 1rem;
}

.data-display-title {
	font-size: 1.125rem;
	font-weight: 500;
	margin-bottom: 0.5rem;
}

/* Status indicators */
.status-indicator {
	width: 0.5rem;
	height: 0.5rem;
	border-radius: 9999px;
	display: inline-block;
	margin-right: 0.25rem;
}

.status-online {
	background-color: var(--color-success);
}

.status-offline {
	background-color: var(--color-error);
}

.status-away {
	background-color: var(--color-warning);
}

/* Empty states */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	text-align: center;
	padding: 2rem;
}

.empty-state-icon {
	color: color-mix(in srgb, var(--color-base-content) 30%, transparent);
	margin-left: auto;
	margin-right: auto;
	margin-bottom: 1rem;
}

.empty-state-title {
	font-size: 1.125rem;
	font-weight: 700;
	margin-bottom: 0.5rem;
}

.empty-state-message {
	color: color-mix(in srgb, var(--color-base-content) 70%, transparent);
	margin-bottom: 1rem;
}

/* Loading states */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-top: 2rem;
	padding-bottom: 2rem;
}

.loading-text {
	margin-left: 1rem;
	color: color-mix(in srgb, var(--color-base-content) 70%, transparent);
}

/* Animations */
.animate-fade-in {
	animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
	animation: slideUp 0.3s ease-out;
}

.animate-pulse {
	animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(10px);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

@keyframes pulse {
	0%,
	100% {
		opacity: 1;
	}
	50% {
		opacity: 0.5;
	}
}


/* Typography helpers */
.text-emphasis {
	font-weight: 500;
}

.text-muted {
	opacity: 0.7;
}

.text-truncate {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
