/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from 'kysely';

export type Generated<T> =
	T extends ColumnType<infer S, infer I, infer U>
		? ColumnType<S, I | undefined, U>
		: ColumnType<T, T | undefined, T>;

export type Numeric = ColumnType<string, number | string, number | string>;

export type Timestamp = ColumnType<Date, Date | string, Date | string>;

export interface Courses {
	abr: string;
	code: Generated<string>;
	createdAt: Generated<Timestamp | null>;
	name: string;
	order: Generated<number>;
	userCode: string;
}

export interface EvalAnswers {
	code: Generated<string>;
	questionCode: string;
	registerCode: string;
	studentAnswer: string | null;
}

export interface EvalQuestions {
	code: Generated<string>;
	correctKey: string;
	evalCode: string;
	omitable: Generated<boolean | null>;
	orderInEval: number;
	scorePercent: Generated<Numeric>;
	sectionCode: string;
}

export interface EvalResults {
	blankCount: Generated<number>;
	calculatedAt: Generated<Timestamp | null>;
	code: Generated<string>;
	correctCount: Generated<number>;
	evalCode: string;
	incorrectCount: Generated<number>;
	registerCode: string;
	score: Generated<Numeric>;
	sectionCode: string | null;
}

export interface Evals {
	code: Generated<string>;
	createdAt: Generated<Timestamp | null>;
	evalDate: Timestamp;
	groupName: string;
	levelCode: string;
	name: string;
	updatedAt: Generated<Timestamp | null>;
	userCode: string;
}

export interface EvalSections {
	code: Generated<string>;
	courseCode: string;
	evalCode: string;
	orderInEval: number;
	questionCount: number;
}

export interface Levels {
	abr: string;
	code: Generated<string>;
	createdAt: Generated<Timestamp | null>;
	name: string;
	users: Generated<string[]>;
}

export interface Permissions {
	action: string;
	code: Generated<string>;
	createdAt: Generated<Timestamp>;
	entity: string;
	userCode: string;
}

export interface Registers {
	code: Generated<string>;
	createdAt: Generated<Timestamp | null>;
	groupName: string;
	levelCode: string;
	rollCode: string;
	studentCode: string;
	userCode: string;
}

export interface Students {
	code: Generated<string>;
	createdAt: Generated<Timestamp | null>;
	email: string;
	lastName: string;
	name: string;
	phone: string | null;
	updatedAt: Generated<Timestamp | null>;
	userCode: string;
}

export interface Users {
	code: Generated<string>;
	createdAt: Generated<Timestamp>;
	email: string;
	isEmailVerified: Generated<boolean>;
	isSuperAdmin: Generated<boolean>;
	lastLogin: Timestamp | null;
	lastName: string | null;
	name: string | null;
	passwordHash: string;
	photoUrl: string | null;
	updatedAt: Generated<Timestamp>;
}

export interface DB {
	courses: Courses;
	evalAnswers: EvalAnswers;
	evalQuestions: EvalQuestions;
	evalResults: EvalResults;
	evals: Evals;
	evalSections: EvalSections;
	levels: Levels;
	permissions: Permissions;
	registers: Registers;
	students: Students;
	users: Users;
}
