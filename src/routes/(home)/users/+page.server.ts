// routes/users/+page.server.ts
import type { Actions, PageServerLoad } from './$types';
import { fail } from '@sveltejs/kit';
import { hash } from 'bcryptjs';

export const load: PageServerLoad = async ({ depends, locals }) => {
	depends('users:load');

	try {
		const users = await locals.db
			.selectFrom('users')
			.select([
				'code as id',
				'email',
				'name',
				'lastName as last_name',
				'photoUrl as photo_url',
				'isEmailVerified as email_confirmed_at',
				'createdAt as created_at',
				'lastLogin as last_sign_in_at'
			])
			.execute();

		// Transform to match expected format
		const transformedUsers = users.map((user) => ({
			...user,
			user_metadata: {
				name: user.name,
				last_name: user.last_name,
				photo_url: user.photo_url
			}
		}));

		return { users: transformedUsers, title: 'Usuarios' };
	} catch (error) {
		console.error('Error loading users:', error);
		return { users: [], title: 'Usuarios' };
	}
};

export const actions: Actions = {
	create: async ({ request, locals }) => {
		const formData = await request.formData();
		const email = formData.get('email') as string;
		const name = formData.get('name') as string;
		const last_name = formData.get('last_name') as string;
		const password = formData.get('password') as string;
		const photo_url = (formData.get('photo_url') as string) || 'avatar.svg';

		try {
			// Check if user already exists
			const existingUser = await locals.db
				.selectFrom('users')
				.select('code')
				.where('email', '=', email.toLowerCase())
				.executeTakeFirst();

			if (existingUser) {
				return fail(400, { error: 'El usuario ya existe' });
			}

			// Hash password
			const passwordHash = await hash(password, 12);

			// Create user
			await locals.db
				.insertInto('users')
				.values({
					email: email.toLowerCase(),
					passwordHash,
					name,
					lastName: last_name,
					photoUrl: photo_url,
					isEmailVerified: true, // Auto-confirm for admin created users
					isSuperAdmin: false
				})
				.execute();

			return { success: true };
		} catch (error: any) {
			console.error('Error creating user:', error);
			return fail(400, { error: error.message || 'Error al crear usuario' });
		}
	},

	update: async ({ request, locals }) => {
		const formData = await request.formData();
		const userId = formData.get('user_id') as string;
		const email = formData.get('email') as string;
		const name = formData.get('name') as string;
		const last_name = formData.get('last_name') as string;
		const photo_url = (formData.get('photo_url') as string) || 'avatar.svg';

		try {
			await locals.db
				.updateTable('users')
				.set({
					email: email.toLowerCase(),
					name,
					lastName: last_name,
					photoUrl: photo_url
				})
				.where('code', '=', userId)
				.execute();

			return { success: true };
		} catch (error: any) {
			console.error('Error updating user:', error);
			return fail(400, { error: error.message || 'Error al actualizar usuario' });
		}
	},

	updatePassword: async ({ request, locals }) => {
		const formData = await request.formData();
		const userId = formData.get('user_id') as string;
		const password = formData.get('password') as string;

		try {
			// Hash new password
			const passwordHash = await hash(password, 12);

			await locals.db
				.updateTable('users')
				.set({ passwordHash })
				.where('code', '=', userId)
				.execute();

			return { success: true };
		} catch (error: any) {
			console.error('Error updating password:', error);
			return fail(400, { error: error.message || 'Error al actualizar contraseña' });
		}
	},

	delete: async ({ request, locals }) => {
		const formData = await request.formData();
		const userId = formData.get('user_id') as string;

		// Check if trying to delete self
		if (locals.user?.code === userId) {
			return fail(400, { error: 'No puedes eliminar a ti mismo' });
		}

		try {
			await locals.db.deleteFrom('users').where('code', '=', userId).execute();

			return { success: true };
		} catch (error: any) {
			console.error('Error deleting user:', error);
			return fail(400, { error: error.message || 'Error al eliminar usuario' });
		}
	}
};
